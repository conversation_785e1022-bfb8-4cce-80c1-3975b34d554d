#!/usr/bin/env python3
"""
Test script to verify <PERSON><PERSON><PERSON> permissions after role upgrade.
Run this after your role is upgraded from <PERSON>er to Editor.
"""

import requests
import json
from datetime import datetime

def test_langsmith_permissions():
    api_key = '***************************************************'
    headers = {'x-api-key': api_key, 'Content-Type': 'application/json'}
    
    print("🔍 Testing LangSmith API permissions...")
    
    # Test 1: Check workspace access
    print("\n1. Testing workspace access...")
    try:
        response = requests.get('https://api.smith.langchain.com/workspaces', headers=headers)
        if response.status_code == 200:
            workspaces = response.json()
            workspace = workspaces[0]
            print(f"✅ Workspace: {workspace['display_name']}")
            print(f"   Role: {workspace['role_name']}")
            print(f"   Permissions: {', '.join(workspace['permissions'])}")
            
            # Check if runs:create permission exists
            if 'runs:create' in workspace['permissions']:
                print("✅ Has runs:create permission!")
            else:
                print("❌ Missing runs:create permission")
                return False
        else:
            print(f"❌ Workspace access failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error checking workspace: {e}")
        return False
    
    # Test 2: Try to create a test run
    print("\n2. Testing run creation...")
    test_run = {
        'name': 'permission-test-run',
        'run_type': 'chain',
        'inputs': {'test': 'permission check'},
        'project_name': 'rathid-gdk'
    }
    
    try:
        response = requests.post('https://api.smith.langchain.com/runs', headers=headers, json=test_run)
        if response.status_code in [200, 201]:
            print("✅ Successfully created test run!")
            run_data = response.json()
            print(f"   Run ID: {run_data.get('id', 'N/A')}")
            print(f"   Project: {run_data.get('session_name', 'N/A')}")
            return True
        else:
            print(f"❌ Run creation failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Error creating run: {e}")
        return False

if __name__ == "__main__":
    success = test_langsmith_permissions()
    if success:
        print("\n🎉 LangSmith permissions are working correctly!")
        print("   You can now proceed with DAP tracing setup.")
    else:
        print("\n❌ LangSmith permissions are still insufficient.")
        print("   Please contact your workspace admin to upgrade to Editor role.")
