#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to view local LangSmith traces stored in the filesystem.
This works when LANGSMITH_TRACING=local is set.
"""

import os
import json
import sqlite3
from datetime import datetime
from pathlib import Path
import argparse

def find_langsmith_db():
    """Find the LangSmith local database file."""
    # Common locations for LangSmith local storage
    possible_paths = [
        Path.home() / ".langsmith" / "traces.db",
        Path.home() / ".cache" / "langsmith" / "traces.db", 
        Path("/tmp") / "langsmith" / "traces.db",
        Path.cwd() / ".langsmith" / "traces.db",
        Path.cwd() / "traces.db"
    ]
    
    for path in possible_paths:
        if path.exists():
            return path
    
    # Search for any .db files that might be LangSmith traces
    for root, dirs, files in os.walk(Path.home()):
        for file in files:
            if file.endswith('.db') and 'langsmith' in file.lower():
                return Path(root) / file
    
    return None

def view_traces_from_db(db_path, limit=10):
    """View traces from SQLite database."""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get table names
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        print(f"📊 Tables in {db_path}: {[t[0] for t in tables]}")
        
        # Try to find runs/traces table
        for table_name in ['runs', 'traces', 'langsmith_runs']:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                count = cursor.fetchone()[0]
                print(f"📈 Found {count} records in {table_name}")
                
                # Get recent traces
                cursor.execute(f"""
                    SELECT * FROM {table_name} 
                    ORDER BY start_time DESC 
                    LIMIT {limit}
                """)
                
                columns = [description[0] for description in cursor.description]
                rows = cursor.fetchall()
                
                print(f"\n🔍 Recent traces from {table_name}:")
                for i, row in enumerate(rows):
                    print(f"\n--- Trace {i+1} ---")
                    for col, val in zip(columns, row):
                        if isinstance(val, str) and len(val) > 100:
                            val = val[:100] + "..."
                        print(f"  {col}: {val}")
                        
            except sqlite3.OperationalError:
                continue
                
        conn.close()
        
    except Exception as e:
        print(f"❌ Error reading database: {e}")

def view_traces_from_json():
    """Look for JSON trace files."""
    possible_dirs = [
        Path.home() / ".langsmith",
        Path.home() / ".cache" / "langsmith",
        Path("/tmp") / "langsmith",
        Path.cwd() / ".langsmith"
    ]
    
    for trace_dir in possible_dirs:
        if trace_dir.exists():
            print(f"📁 Checking {trace_dir}")
            json_files = list(trace_dir.glob("*.json"))
            if json_files:
                print(f"📄 Found {len(json_files)} JSON files")
                for json_file in json_files[:5]:  # Show first 5
                    try:
                        with open(json_file) as f:
                            data = json.load(f)
                        print(f"\n📄 {json_file.name}:")
                        print(f"   Keys: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")
                        if isinstance(data, dict) and 'name' in data:
                            print(f"   Name: {data.get('name')}")
                            print(f"   Type: {data.get('run_type', 'unknown')}")
                            print(f"   Start: {data.get('start_time', 'unknown')}")
                    except Exception as e:
                        print(f"   Error reading {json_file}: {e}")

def monitor_logs():
    """Monitor service logs for trace information."""
    log_files = [
        "log/duo-workflow-service/current",
        "log/gitlab-ai-gateway/gateway_debug.log",
        "sv/duo-workflow-service/log/current"
    ]
    
    print("📋 Recent trace-related log entries:")
    for log_file in log_files:
        log_path = Path(log_file)
        if log_path.exists():
            print(f"\n📄 {log_file}:")
            try:
                with open(log_path) as f:
                    lines = f.readlines()
                    # Look for trace-related lines
                    trace_lines = [line for line in lines[-50:] if any(keyword in line.lower() for keyword in ['trace', 'langsmith', 'run', 'workflow'])]
                    for line in trace_lines[-10:]:  # Last 10 trace-related lines
                        print(f"   {line.strip()}")
            except Exception as e:
                print(f"   Error reading {log_file}: {e}")

def main():
    parser = argparse.ArgumentParser(description="View local LangSmith traces")
    parser.add_argument("--limit", type=int, default=10, help="Number of traces to show")
    parser.add_argument("--logs", action="store_true", help="Show service logs instead")
    args = parser.parse_args()
    
    print("🔍 Looking for local LangSmith traces...")
    
    if args.logs:
        monitor_logs()
        return
    
    # Try to find database
    db_path = find_langsmith_db()
    if db_path:
        print(f"📊 Found LangSmith database: {db_path}")
        view_traces_from_db(db_path, args.limit)
    else:
        print("❌ No LangSmith database found")
    
    # Try to find JSON files
    print("\n🔍 Looking for JSON trace files...")
    view_traces_from_json()
    
    # Show logs as fallback
    print("\n📋 Checking service logs for trace activity...")
    monitor_logs()

if __name__ == "__main__":
    main()
